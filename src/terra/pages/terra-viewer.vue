<script setup>
import { keyBy, uniqBy } from 'lodash-es';
import { onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import { useModal } from 'vue-final-modal';
import { useRoute, useRouter } from 'vue-router';
import { useAuthStore } from '~/auth/stores/auth.store';
import { useMapItemCreation } from '~/common/composables/mapbox/creation-icons';
import { useMap } from '~/common/composables/mapbox/maps';
import { useMapboxFeatureSelection } from '~/common/composables/mapbox/selection';
import useEmitter from '~/common/composables/useEmitter';
import { useCustomViewStore } from '~/common/stores/custom-view.store.js';
import { usePusherStore } from '~/common/stores/pusher.store.js';
import TerraNewForm from '~/forms/components/new-form/terra-new-form.vue';
import { useFormPermissions } from '~/forms/composables/form-permissions.composable';
import { useFormsStore } from '~/forms/store/forms.store';
import TerraTaskForm from '~/tasks/components/molecule/task-form/terra-task-form.vue';
import { useTasksStore } from '~/tasks/store/tasks.store';
import ConfirmLocation from '~/terra/components/confirm-location.vue';
import CreateItems from '~/terra/components/create-shortcut.vue';
import Elevation from '~/terra/components/map-tools/elevation.vue';
import Scale from '~/terra/components/map-tools/scale.vue';
import Volume from '~/terra/components/map-tools/volume.vue';
import MbTilesPopup from '~/terra/components/mb-tiles-popup.vue';
import TerraInitializeNaavixAgent from '~/terra/components/terra-initialize-naavix-agent.vue';
import TerraSidebarMenu from '~/terra/components/terra-sidebar-menu.vue';
import TerraViewerFooter from '~/terra/components/terra-viewer-footer.vue';
import YieldEstimator from '~/terra/components/yield-estimator.vue';
import { toggleLabels, useFeatureSelection } from '~/terra/composables/use-common-helpers';
import RestoreView from '~/terra/molecules/restore-view.vue';
import { useTerraStore } from '~/terra/store/terra.store.js';
import { useFeatureCluster } from '~/terra/utils/feature-cluster.composable';
import { useThermHelperComposable } from '~/therm/composables/helper-composable.js';
import MapTools from '../components/map-tools.vue';
import { useTerraHelperComposable } from '../utils/helper-composable';

const task_form_centroid = ref(null);
const marker_location = ref(null);
const terra_store = useTerraStore();
const emitter = useEmitter();
const forms_store = useFormsStore('terra_form_store');
const custom_view_store = useCustomViewStore();
const auth_store = useAuthStore();

const route = useRoute();
const router = useRouter();
const $t = inject('$t');
const $toast = inject('$toast');
const task_store = useTasksStore('terra_task_store');

watchEffect(() => {
  const pusher_store = usePusherStore();
  if (pusher_store.is_initialized) {
    task_store.subscribe_pusher({
      organization: auth_store.current_organization?.uid,
      ...(route.params.asset_id ? { asset: route.params.asset_id } : {}),
    });
  }
}, { flush: 'post' });

// General
const active_tool = ref('');
const filters_active = ref(false);

const create_enable = ref(false);

const settings_active = ref(false);

// Task/form icon on map
const open_task_popup = ref(false);
const open_form_popup = ref(false);
const task_marker_active = ref(false);
const form_marker_active = ref(false);
const project_details_for_task_form_creation = ref(null);

// Yield estimator
const yield_estimator_marker = ref(null);
const is_yield_estimator_active = ref(false);
const yield_estimator_coordinates = ref([]);

// Tools
const guide_enable = ref(false);
const visualizer_enable = ref(false);
const scale_enable = ref(false);

const { loadTaskFormIcons, addSymbols, dragLayers } = useTerraHelperComposable();
const { flyToAssociatedFeature } = useThermHelperComposable();
const { syncAndGetButtonState } = useFormPermissions();
const { addFeatureClusters } = useFeatureCluster();
provide('$form_create_permission', syncAndGetButtonState());

// Map style
const map_style = ref(localStorage.getItem('map_style') || 'mapbox://styles/mapbox/streets-v11');

const sidebar_state = reactive({
  active_menu: 'layers',
  is_table_active: false,
});

// Computed Properties
const disabled_map_tools = computed(() => {
  const rectangle_and_circle_disabled = terra_store.settings.enable_snap
    ? ['add-rectangle', 'add-circle']
    : [];

  return [
    ...rectangle_and_circle_disabled,
  ];
});

function sourcesLayers() {
  const sources = ['all_features_source', 'symbol-source'];

  const layers
   = [
     {
       id: 'point_feature_layer',
       type: 'circle',
       source: 'all_features_source',
       filter: ['==', '$type', 'Point'],
     },
     {
       id: 'polygon_feature_layer',
       type: 'fill',
       source: 'all_features_source',
       paint: {
         'fill-opacity': 0,
       },
     },
     {
       id: 'linestring_feature_layer',
       type: 'line',
       source: 'all_features_source',
       paint: {
         'line-width': 2,
       },
     },
     {
       id: 'image-pattern-layer',
       type: 'fill',
       source: 'all_features_source',
       paint: {
         'fill-pattern': 'pattern-0',
       },
     },
     {
       id: 'symbol-layer-icon',
       type: 'symbol',

       source: 'symbol-source',
       layout: {
         'icon-image': ['coalesce', ['get', 'icon'], 'marker'],
         'icon-size': ['coalesce', ['get', 'icon_size'], 1],
         'icon-allow-overlap': true,
       },
     },
   ];
  return { sources, layers };
}

const { styleSelection, loadSelectionEvents } = useFeatureSelection({
  active_tool,
  layer_ids: ['polygon_feature_layer', 'linestring_feature_layer', 'point_feature_layer'],
  store: terra_store,
});
const { loadSelectionEvents: loadTaskFormSelectionEvents } = useMapboxFeatureSelection({
  layer_ids: ['symbol-layer-icon'],
  style_selection: false,
  use_normal_selection: true,
  task_form_selection: true,
}, (e, type) => {
  if (!['update', 'create', 'selectionchange'].includes(type)) {
    const tasksForms = uniqBy(e, f => f.properties.uid).map(item => ({
      geometry: item.geometry || item._geometry,
      id: item.id,
      properties: item.properties,
      type: item.type,
    }));
    if (tasksForms.length === 1 && type === 'click') {
      const task_form = tasksForms[0].properties;
      logger.log(tasksForms);
      if (task_form.feature_type === 'task') {
        router.push({
          ...route,
          query: {
            task: btoa(JSON.stringify({
              id: task_form.task_form_uid,
              store_key: 'terra_task_store',
            })),
          },
        });
      }

      else {
        const form_detail = JSON.parse(task_form.data);
        router.push({
          ...route,
          query: {
            form: btoa(JSON.stringify({
              form_uid: task_form.task_form_uid,
              feature_uid: form_detail.target_element?.stage === 'TERRA' && form_detail.target_element?.type === 'feature' ? form_detail.target_element.uid : null,
              store_key: 'terra_form_store',
            })),
          },
        });
      }

      return;
    }
    terra_store.selected_tasks_forms = keyBy(tasksForms, feature => feature.properties.task_form_uid);

    const symbolSourceItems = terra_store.map.getSource('symbol-source')._data;
    symbolSourceItems.features = symbolSourceItems.features.map((f) => {
      f.properties.icon = f.properties.icon.replace('-selected', '');
      if (terra_store.selected_tasks_forms[f.properties.task_form_uid])
        f.properties.icon += '-selected';

      return f;
    });

    terra_store.map.getSource('symbol-source').setData(symbolSourceItems);
  }
});
const mb_tiles_popup = useModal({
  component: MbTilesPopup,
  attrs: {
    onClose() {
      mb_tiles_popup.close();
    },
  },
});
watch(() => terra_store.polygon, (val) => {
  if (val) {
    getTasksAndForms();
  }
  else {
    task_store.tasks_map = {};
    forms_store.forms_map = {};
    addSymbols();
  }
});
watch(() => route.params.asset_id, (val) => {
  if (val)
    router.push({ name: 'maps-list', params: { type: 'maps-list', asset_id: val } });
});
watch(() => terra_store.selected_features, (val) => {
  styleSelection(val, {
    mapbox_instance: terra_store.map,
    colors_map_accessor: 'featureTypeId',
    colors_map: terra_store.feature_types,
  });
  if (val.length === 0 && !terra_store.is_creating_vector)
    terra_store.clear_gl_draw();
});
watch(() => terra_store.features_on_map, () => {
  if (terra_store.settings.display_labels)
    toggleLabels();
});
// Have one state flag and watch over that to update the symbols
watch(() => [task_store?.tasks(), forms_store.forms, terra_store.gallery_view_state.is_active, terra_store.update_symbols_on_the_map_flag], () => {
  addSymbols();
  if (terra_store.settings.display_labels)
    toggleLabels();
});
watch(() => ({
  update_symbols_on_the_map_flag: terra_store.update_symbols_on_the_map_flag,
  show_counts: terra_store.settings.show_counts,
  gallery_view_state: terra_store.gallery_view_state.is_active,
}), (new_state, old_state) => {
  // Check if show_counts changed and gallery_view_state is active
  if (new_state.show_counts === true && new_state.gallery_view_state && new_state.gallery_view_state !== old_state.gallery_view_state) {
    terra_store.settings.show_counts = false;
  }

  // Check if gallery_view_state changed and show_counts is active
  if (new_state.gallery_view_state === true && new_state.show_counts && new_state.show_counts !== old_state.show_counts) {
    terra_store.gallery_view_state.is_active = false;
  }

  // Trigger the function to add feature clusters
  addFeatureClusters(terra_store, terra_store.visible_features_on_map);
});
watch(() => terra_store.show_request_mbtiles_popup?.requested_reports, () => {
  if (terra_store.show_request_mbtiles_popup?.resolved_all_requests && Object.keys(terra_store.show_request_mbtiles_popup?.requested_reports || {}).length)
    mb_tiles_popup.open();
}, {
  deep: true,
});
watch(() => terra_store.filters_state.quick_filter, (val) => {
  if (val)
    sidebar_state.active_menu = 'filters';
});
const { initMapbox, loadMapBoxPackage, addMapboxToken, initDraw, setSources, setLayers, removeMapboxInstance } = useMap({}, async (event_data, event_name) => {
  if (event_name === 'loaded') {
    loadSelectionEvents({ mapbox_instance: terra_store.map });
    loadTaskFormSelectionEvents({ mapbox_instance: terra_store.map });
    loadTaskFormIcons({ map: terra_store.map });
    getData();

    const { sources, layers } = sourcesLayers();

    setSources(sources, terra_store.map);
    setLayers(layers, terra_store.map);
    terra_store.draw = await initDraw(terra_store.map);
    dragLayers({
      layer: 'symbol-layer-icon',
      source: 'symbol-source',
      save_location: false,
      layer_type: 'task/form',
      map_instance: terra_store.map,
    });
    terra_store.map.on('click', () => {
      if (!['add', 'scale', 'volume', 'elevation', 'create', 'add-polygon', 'add-rectangle', 'add-circle'].includes(active_tool.value))
        active_tool.value = '';
    });
  }
});
const { addIconMarkerForItemCreation, removeItemCreationIcon, iconMarkerForItemCreationSource } = useMapItemCreation({}, (e) => {
  task_form_centroid.value = e.lngLat;
  marker_location.value = { ...e };
});

async function getData(view_config = null) {
  try {
    terra_store.is_loading = true;
    await terra_store.set_ftg_and_update_features_styles({
      type: 'terra',
      uid: route.params.id,
    });

    await terra_store.set_container({
      uid: route.params.id,
      type: 'terra',
    });
    terra_store.terra_track_events('Viewed');
    terra_store.set_terra_workflows({
      asset_id: route.params.asset_id,
      terra_workflows: Object.keys(terra_store.terra_workflows).length ? terra_store.terra_workflows : null,
    });

    if (!terra_store.sm_instances_map.is_fetched && auth_store.check_split('merge_sm_data_with_terra_features')) {
      terra_store.set_sm_instances({
      });
    }

    const first_project = Object.values(
      Object.values(terra_store.container.groups)[0]?.projects || {},
    )[0];

    const custom_view_data = custom_view_store.views_map[route.query?.view_uid];
    const config = view_config || custom_view_data?.data;

    const view_present = await checkAndUpdateView(config);

    if (route.query?.metadata) {
      await flyToAssociatedFeature('terra');
    }
    else if (!view_present) {
      await terra_store.toggle_project({ project: first_project });
      terra_store.update_features_on_map_flag += 1;
    }

    toggleLabels();
    terra_store.load_patterns();
    await terra_store.update_map_features_and_polygon();
    if (!view_present) {
      sidebar_state.active_menu = 'layers';
      sidebar_state.is_table_active = false;
    }
    terra_store.is_loading = false;
  }
  catch (err) {
    logger.log(err);
    if (route.query?.metadata) {
      $toast({
        title: $t('Location not found'),
        text: $t('Can not navigate to the location. You don\'t have access, or the location is no longer available'),
        type: 'warning',
      });
    }

    terra_store.is_loading = false;
  }
}
async function getTasksAndForms(options = { tasks: true, forms: true }) {
  try {
    terra_store.is_loading = true;
    if (auth_store.has_tasks_access && options.tasks) {
      await task_store.set_tasks({
        stage: 'TERRA',
        archive: false,
        page_size: Number.MAX_SAFE_INTEGER,
        page_number: 1,
        is_template: false,
        polygon: terra_store.polygon,
        asset_uid: route.params.asset_id,
      });
    }
    if (auth_store.has_forms_access && options.forms) {
      await forms_store.set_forms({
        query: {
          stage: 'TERRA',
          status: 'published',
          polygon: terra_store.polygon,
          is_child: true,
          all_access: true,
          asset_uid: route.params.asset_id,

        },
      });
    }
    addSymbols();
    terra_store.is_loading = false;
  }
  catch (err) {
    logger.log(err);
    terra_store.is_loading = false;
  }
}

async function checkAndUpdateView(config) {
  if (!config)
    return false;
  if (config?.active_groups?.length) {
    const container_copy = terra_store.container || {};
    const toggleGroup = async (uid) => {
      terra_store.active_group_map[uid].ortho_enabled = true;
      terra_store.active_group_map[uid].features_enabled = true;

      terra_store.projects_request_status.show = true;
      // Update group data for features and
      const group = await terra_store.set_group({ group: terra_store.container.groups[uid], container: terra_store.container });

      terra_store.projects_request_status = {
        total: 0,
        current: 0,
        cancel: false,
        show: false,
      };
      // Replace the group in container with updated group
      container_copy.groups[uid] = group;
    };
    await Promise.all(config.active_groups.map(group_uid => toggleGroup(group_uid)));
    terra_store.set_container({ container: container_copy });
  }
  if (config?.filtered_active_projects?.length) {
    const projects = [];
    config.filtered_active_projects.forEach((project_uid) => {
      const project = terra_store.active_projects_data_map({ all_projects: true })[project_uid];
      terra_store.active_projects_map[project.uid] = {
        ortho_enabled: true,
        features_enabled: true,
      };
      projects.push(project);
    });
    await terra_store.set_projects_essentials({ projects });
  }
  // trigger watcher in terra filter for current features
  terra_store.update_features_on_map_flag += 1;

  if (config.center) {
    terra_store.map.flyTo({
      center: [config.center.lng, config.center.lat],
      zoom: config.zoom_level || 18,
      speed: 4,
    });
  }
  if (config.inactive_feature_types)
    terra_store.inactive_feature_types = config.inactive_feature_types;
  if (config.tasks_cluster)
    terra_store.tasks_cluster = config.tasks_cluster;
  if (config.forms_cluster)
    terra_store.forms_cluster = config.forms_cluster;
  if (config.feature_config)
    terra_store.settings = { ...terra_store.settings, ...config.feature_config };
  if (config.table_state)
    terra_store.table_state = config.table_state;
  if (config.filters_state) {
    terra_store.filters_state = config.filters_state;
    terra_store.filters_state.features_on_map = terra_store.features;
    if (terra_store.filters_state.is_mounted) {
      sidebar_state.active_menu = 'filters';
      await terra_store.map.once('idle');
    }
    terra_store.update_features_on_map_flag += 1; // trigger watcher in terra filter
  }
  if (config.sidebar_menu) {
    sidebar_state.active_menu = config.sidebar_menu.active_menu;
    sidebar_state.is_table_active = config.sidebar_menu.is_table_active;
    terra_store.update_features_on_map_flag += 1; // trigger watcher in terra filter
  }
  else {
    sidebar_state.active_menu = 'layers';
  }
  return true;
}

function switchDrawMode(mode) {
  if (terra_store.draw.getMode() === mode) {
    terra_store.draw.trash();
    active_tool.value = '';
  }
  else {
    terra_store.draw.changeMode(mode);
  }
}

function addEvents() {
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      terra_store.draw.changeMode('simple_select');
      document.removeEventListener('keydown');
    }
  });
}

function onYieldEstimatorClick() {
  is_yield_estimator_active.value = !is_yield_estimator_active.value;
  if (is_yield_estimator_active.value) {
    const center = terra_store.map.getCenter();
    logger.log('🚀 ~ file: terra-viewer.vue:390 ~ onYieldEstimatorClick ~ center:', center);
    if (!yield_estimator_marker.value) {
      const element = document.createElement('div');
      element.className = 'custom-yield-marker';

      element.style.backgroundImage = 'url(/img/icons/location-pin.png)';

      element.style.width = `${35}px`;
      element.style.height = `${35}px`;
      element.style.backgroundSize = '100%';
      logger.log('🚀 ~ file: terra-viewer.vue:321 ~ onYieldEstimatorClick ~ element:', element);
      yield_estimator_marker.value = new mapboxgl.Marker({
        element,
        draggable: true,
      })
        .setLngLat([center.lng, center.lat])
        .addTo(terra_store.map);
      yield_estimator_coordinates.value = [center.lng, center.lat];
    }

    const onDragEnd = () => {
      const lngLat = yield_estimator_marker.value.getLngLat();
      yield_estimator_coordinates.value = [lngLat.lng, lngLat.lat];
    };

    yield_estimator_marker.value.on('dragend', onDragEnd);
  }
  else {
    yield_estimator_marker.value.remove();
    yield_estimator_marker.value = null;
    active_tool.value = '';
  }
}

async function onToolActivated(tool_event_name) {
  if (active_tool.value === 'yield_estimator' && tool_event_name !== 'yield_estimator')
    onYieldEstimatorClick();
  if (
    ![
      'measure',
      'guide',
      'scale',
      'visualizer',
      'volume',
      'elevation',
      'filters',
      'settings',
      'yield_estimator',
      'create',
    ].includes(tool_event_name)
  ) {
    active_tool.value = tool_event_name;
  }
  if (['add-point', 'add-polygon', 'add-rectangle', 'add-line', 'add-circle'].includes(tool_event_name))
    terra_store.is_creating_vector = true;

  switch (tool_event_name) {
    case 'add-point':
      switchDrawMode('draw_point');

      addEvents();
      break;
    case 'add-polygon':
      switchDrawMode('draw_polygon');
      addEvents();
      break;
    case 'add-rectangle':
      switchDrawMode('draw_rectangle');
      addEvents();
      break;
    case 'add-line':
      switchDrawMode('draw_line_string');
      addEvents();
      break;
    case 'add-circle':
      switchDrawMode('drag_circle');
      addEvents();
      break;
    case 'scale':
    case 'volume':
    case 'yield_estimator':
    case 'elevation':
    case 'settings':
    case 'filters':
    case 'create':
      active_tool.value = active_tool.value === tool_event_name ? '' : tool_event_name;
      break;
  }
}

function disableCreationMarker() {
  open_task_popup.value = false;
  open_form_popup.value = false;
  task_marker_active.value = false;
  form_marker_active.value = false;
  removeItemCreationIcon(terra_store.map);
  marker_location.value = null;
  project_details_for_task_form_creation.value = null;
}
function toggleCreation(type) {
  active_tool.value = '';

  if (type === 'tasks') {
    if (terra_store.selected_features.length) {
      open_task_popup.value = true;
      return;
    }
    task_marker_active.value = !task_marker_active.value;
    if (!task_marker_active.value) {
      disableCreationMarker();
      return;
    }
  }
  else if (type === 'forms') {
    if (terra_store.selected_features.length) {
      open_form_popup.value = true;
      return;
    }
    form_marker_active.value = !form_marker_active.value;
    if (!form_marker_active.value) {
      disableCreationMarker();
      return;
    }
  }

  addIconMarkerForItemCreation({
    map_instance: terra_store.map,
    icon: 'location-marker',
  });
  if (task_marker_active.value || form_marker_active.value)
    iconMarkerForItemCreationSource({ lngLat: terra_store.map.getCenter() }, terra_store.map);
}

async function handleMapStyleChange() {
  try {
    if (map_style.value.includes('satellite')) {
      terra_store.map.setStyle('mapbox://styles/mapbox/streets-v11');
      map_style.value = 'mapbox://styles/mapbox/streets-v11';
    }
    else {
      terra_store.map.setStyle('mapbox://styles/mapbox/satellite-v8');
      map_style.value = 'mapbox://styles/mapbox/satellite-v8';
    }

    localStorage.setItem('map_style', map_style.value);

    setTimeout(async () => {
      terra_store.is_loading = true;
      loadTaskFormSelectionEvents({ mapbox_instance: terra_store.map });
      loadTaskFormIcons({ map: terra_store.map });
      const { sources, layers } = sourcesLayers();

      sources.forEach((key) => {
        if (terra_store.map.getSource(key))
          terra_store.map.removeSource(key);
      });
      layers.forEach((key) => {
        if (terra_store.map.getLayer(key))
          terra_store.map.removeLayer(key);
      });
      setSources(sources, terra_store.map);
      setLayers(layers, terra_store.map);
      if (!terra_store.active_projects.length) {
        const first_project = Object.values(
          Object.values(terra_store.container.groups)[0]?.projects || {},
        )[0];
        terra_store.active_projects_map[first_project.uid].ortho_enabled = terra_store.active_projects_map[first_project.uid].features_enabled = false;

        await terra_store.toggle_project({ project: first_project });
      }
      else {
        await Promise.all(terra_store.active_projects.map(async ({ group_uid, uid }) => {
          terra_store.active_projects_map[uid].ortho_enabled = terra_store.active_projects_map[uid].features_enabled = false;
          const project = terra_store.container.groups?.[group_uid]?.projects?.[uid] || { group_uid, uid };
          return await terra_store.toggle_project({ project });
        }));
      }

      terra_store.load_patterns();
      terra_store.update_features_styles();
      terra_store.update_map_features_and_polygon();
      loadSelectionEvents({
        mapbox_instance: terra_store.map,

      });
      await getTasksAndForms();
    }, 1500);
  }
  catch (err) {
    terra_store.is_loading = false;
    logger.log(err);
  }
}

function confirmLocation(data) {
  project_details_for_task_form_creation.value = data;
  if (task_marker_active.value)
    open_task_popup.value = true;
  else if (form_marker_active.value)
    open_form_popup.value = true;
}

async function onContainerChange(e) {
  const url = router.resolve({
    name: 'terra-viewer',
    params: {
      asset_id: route.params.asset_id,
      id: e.uid,
    },
  }).href;
  window.location.href = url;
  // try {
  //   terra_store.$reset();
  //   terra_store.is_loading = true;
  //   terra_store.container = e;
  //   terra_store.map = await initMapbox({
  //     container_id: 'terra-viewer',
  //     style: map_style.value,
  //   });

  //   terra_store.is_loading = false;
  // }
  // catch (err) {
  //   terra_store.is_loading = false;
  // }
}

onMounted(async () => {
  try {
    terra_store.is_loading = true;
    await loadMapBoxPackage();

    await addMapboxToken();
    terra_store.map = await initMapbox({
      container_id: 'terra-viewer',
      style: map_style.value,
    });

    terra_store.map.boxZoom.disable();
    // To change icons to draft state once saved
    emitter.on('form_save', () => {
      addSymbols();
    });
  }
  catch (err) {
    logger.error(err);
    terra_store.is_loading = false;
  }
});
onUnmounted(() => {
  removeMapboxInstance(terra_store.map);
  terra_store.$reset();
});

const projects_request_status = computed(() => terra_store.projects_request_status);

const progress_text = computed(() => {
  return `${$t('Loading layers')} (${projects_request_status.value.current}/${projects_request_status.value.total})`;
});

async function exporting() {
  while (projects_request_status.value.show && !projects_request_status.value.cancel) {
    await new Promise((resolve) => {
      setTimeout(() => resolve('loading layers'), 2000);
    });
  }
}

function groupOrProjectToggled() {
  terra_store.show_save_view = true;
  if (terra_store.settings.display_labels)
    toggleLabels();
}

function filterGlDrawConvertedFeatures() {
  const feature_ids = terra_store.draw?.getAll()?.features.map(item => item.properties.uid);
  terra_store.map.setFilter('polygon_feature_layer', [
    'match',
    ['get', 'uid'],
    feature_ids,
    false,
    true,
  ]);
  terra_store.map.setFilter('linestring_feature_layer', [
    'match',
    ['get', 'uid'],
    feature_ids,
    false,
    true,
  ]);
  terra_store.map.setFilter('point_feature_layer', [
    ['==', '$type', 'Point'],
    'match',
    ['get', 'uid'],
    feature_ids,
    false,
    true,
  ]);
}

function handleUpdateTableView(e) {
  sidebar_state.is_table_active = e;
  if (!e) {
    terra_store.table_state = {
      search: '',
      filters: [],
      ordered_keys_map: {},
      sort: [],
      should_sort_selected_features: false,
    };
  }
}

async function changeView(view_config = null) {
  try {
    const projects = Object.entries(terra_store.active_projects_map).filter(([, project]) => project.ortho_enabled).map(([project_uid]) => terra_store.active_projects_data_map({ all_projects: true })[project_uid]);
    terra_store.reset_store_except(['draw', 'map', 'container', 'ftg', 'sm_instances_map', 'terra_workflows']);
    terra_store.parse_container_data({ container: terra_store.container, keep_project_map: false }); // to set active projects map and active groups map
    sidebar_state.is_table_active = false;
    await getData(view_config);
    terra_store.set_projects_ortho({ projects });
  }
  catch (error) {
    logger.log('Error Occurred', error);
    terra_store.is_loading = false;
  }
}
</script>

<template>
  <div class="relative -top-0.5">
    <div v-if="projects_request_status.show && !projects_request_status.cancel">
      <HawkExportToast :key="projects_request_status.current" class="z-[102]" :submit="exporting" :progress_text="progress_text" :completed_text="$t('Loaded blocks successfully')" @cancel="terra_store.projects_request_status.cancel = true">
        <template v-if="projects_request_status.current < projects_request_status.total" #title>
          {{ progress_text }}
        </template>
      </HawkExportToast>
    </div>
    <div v-if="terra_store.is_loading && !projects_request_status.show" class="absolute w-full h-[calc(100vh-65px)] z-[1001] bg-gray-100 opacity-[0.9] flex justify-center items-center">
      <HawkLoader />
    </div>

    <TerraSidebarMenu
      :sidebar_state="sidebar_state"
      @update-table-active="handleUpdateTableView($event)"
      @update-active-menu="sidebar_state.active_menu = $event"
      @open-creation-popup="($event) => {
        if ($event === 'task')
          open_task_popup = true
        else open_form_popup = true
      }"
      @container-change="onContainerChange"
      @set-symbols="addSymbols()"
      @toggle-labels="toggleLabels"
      @group-or-project-toggled="groupOrProjectToggled"
      @save-view="terra_store.show_save_view = true"
    />
    <div>
      <div
        class="ml-5 !h-auto fixed top-16 z-[99] transition-all ease-in-out duration-300"
        :style="`${
          Boolean(sidebar_state.active_menu.length)
            ? `left:370px; width:calc(100vw - ${terra_store.gallery_view_state.is_active ? '800px' : '400px'});`
            : `left:70px; width:calc(100vw - ${terra_store.gallery_view_state.is_active ? '500px' : '100px'});`
        }`"
      >
        <MapTools
          type="terra"
          :active_tools="{
            guide: guide_enable,
            scale: scale_enable,
            create: create_enable,
            visualizer: visualizer_enable,
            create: task_marker_active || form_marker_active,
            settings: settings_active,
            filters: filters_active,
            is_yield_estimator_active,
          }"
          :sidebar_state="sidebar_state"
          :draw_tools_expanded="false"
          :active_tool="active_tool"
          :disabled="disabled_map_tools"
          @tool-activated="onToolActivated"
          @toggle-create="(task_marker_active || form_marker_active) ? disableCreationMarker() : null"
          @toggle-yield-estimator="onYieldEstimatorClick"
          @compare="$router.push({ name: 'terra-compare', params: { ...route.params } })"
          @change-view="changeView"
          @close-save-view="terra_store.show_save_view = false"
          @filter-gl-draw-converted-features="filterGlDrawConvertedFeatures"
          @open-creation-popup="($event) => {
            if ($event === 'task')
              open_task_popup = true
            else open_form_popup = true
          }"
        />

        <CreateItems v-if="active_tool === 'create'" class="absolute right-[274px] mt-10" @create-task="toggleCreation('tasks')" @create-form="toggleCreation('forms')" />

        <ConfirmLocation v-if="marker_location && (task_marker_active || form_marker_active)" :key="marker_location" class="fixed bottom-8 right-8" :marker_location="marker_location" @close="disableCreationMarker" @confirm="confirmLocation" />
        <div class="absolute right-[45px] mt-10">
          <Volume v-if="active_tool === 'volume'" />
          <Elevation v-if="active_tool === 'elevation'" />
          <Scale v-if="active_tool === 'scale'" :selected_features="terra_store.selected_features" />
        </div>
        <TerraTaskForm v-if="open_task_popup" :marker_location="task_form_centroid" opened_from="Direct" :project_details_for_task_form_creation="project_details_for_task_form_creation" class="absolute right-[370px] mt-10" @close="disableCreationMarker" />
        <TerraNewForm v-if="open_form_popup" :marker_location="task_form_centroid" opened_from="Direct" :project_details_for_task_form_creation="project_details_for_task_form_creation" class="absolute right-[370px] mt-10" @close="disableCreationMarker" />
        <YieldEstimator
          v-if="active_tool === 'yield_estimator'"
          class="fixed bottom-3 right-3"
          :coordinates="yield_estimator_coordinates"
          @close="onYieldEstimatorClick()"
        />
        <TerraViewerFooter
          :map-style="map_style"
          :sidebar-state="sidebar_state"
          :task-marker-active="task_marker_active"
          :form-marker-active="form_marker_active"
          @handle-map-style-change="handleMapStyleChange"
          @filter-gl-draw-converted-features="filterGlDrawConvertedFeatures"
          @open-creation-popup="($event) => {
            if ($event === 'task')
              open_task_popup = true
            else open_form_popup = true
          }"
          @update-table-active="handleUpdateTableView"
        />
      </div>

      <RestoreView
        :restore_view_config="terra_store.restore_view_config"
        @handle-restore="changeView($event)"
      />

      <div id="terra-viewer" />
    </div>

    <TerraInitializeNaavixAgent />
  </div>
</template>

<style lang="scss" scoped>
#terra-viewer {
    position: fixed;
    width: 100%;
    top: 65px;
    left: 0;
    bottom: 0;
    right: 0;
    height: 100vh;
}
:deep(.boxdraw)  {
    background: rgba(56, 135, 190, 0.1);
    border: 2px solid #3887be;
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 0;
  }
</style>
