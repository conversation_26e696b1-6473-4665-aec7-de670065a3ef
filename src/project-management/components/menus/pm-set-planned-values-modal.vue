<script setup>
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import localeData from 'dayjs/plugin/localeData';
import { keyBy } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { nextTick, onMounted } from 'vue';
import { useModal } from 'vue-final-modal';
import HawkDeletePopup from '~/common/components/organisms/hawk-delete-popup.vue';
import HawkHandsOnTable from '~/common/components/organisms/hawk-handsontable/hawk-handsontable.vue';
import { useProjectManagementStore } from '~/project-management/store/pm.store';

const props = defineProps({
  parentActivityId: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(['close']);

dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

dayjs.extend(localeData);

const $t = inject('$t');

const project_management_store = useProjectManagementStore();
const { $g, active_schedule_data } = storeToRefs(project_management_store);
const { update_custom_planned_progress, get_custom_planned_progress } = project_management_store;

const state = reactive({
  interval: 'monthly',
  master_data: {}, // Structure: { '<activity_id>': { '<date>': <value> } }
  form_data: {
    current_range: null, // Stores year (example: 2025) in the case of monthly and weekly, and { year, month } in the case of daily
  },
  activity_hierarchy_map: {}, // Structure: { '<parent>': { '<child>': { '<grandchild>': {} } } }
  hot_instance: null,
  hot_data: [],
  longest_wbs_code_length: 0,
  is_loading: false,
  is_saving: false,
  is_dirty: false,
});

let custom_planned_progress_map = {};

const info_modal = useModal({
  component: HawkDeletePopup,
  attrs: {
    button_text: $t('Continue'),
    button_color: 'warning',
    header_icon: IconHawkDashboardPreviewWarning,
    onClose() {
      info_modal.close();
    },
  },
});

const dayjs_instance_of_current_range = computed(() => {
  if (state.interval === 'monthly' || state.interval === 'weekly') {
    return dayjs().year(state.form_data.current_range);
  }
  else if (state.interval === 'daily') {
    return dayjs().year(state.form_data.current_range.year).month(state.form_data.current_range.month);
  }
  return null;
});

const hot_columns = computed(() => {
  let dynamic_date_items = [];
  if (state.interval === 'monthly') {
    dynamic_date_items = dayjs.monthsShort().map((month, index) => {
      const date = dayjs().year(state.form_data.current_range).month(index);
      if (date.isAfter(dayjs(active_schedule_data.value.data[0].end_date), 'month') || date.isBefore(dayjs(active_schedule_data.value.data[0].start_date), 'month'))
        return null;
      return {
        data: `${state.form_data.current_range}-${String(index + 1).padStart(2, '0')}-01`,
        header: `${month} ${state.form_data.current_range}`,
        width: '100px',
      };
    }).filter(Boolean);
  }
  else if (state.interval === 'weekly') {
    const start_of_year = dayjs().year(state.form_data.current_range).startOf('year');
    dynamic_date_items = Array.from({ length: 53 }, (_, i) => {
      const start_of_week = start_of_year.add(i, 'week').startOf('week');
      const end_of_week = start_of_week.endOf('week');
      if (start_of_week.year() !== state.form_data.current_range)
        return null;
      if (start_of_week.isAfter(dayjs(active_schedule_data.value.data[0].end_date)) || end_of_week.isBefore(dayjs(active_schedule_data.value.data[0].start_date)))
        return null;
      return {
        data: start_of_week.format('YYYY-MM-DD'),
        header: `${start_of_week.format('ddd, DD MMM YYYY')}`,
        width: '150px',
      };
    }).filter(Boolean);
  }
  else if (state.interval === 'daily') {
    const daysInMonth = dayjs_instance_of_current_range.value.daysInMonth();
    dynamic_date_items = Array.from({ length: daysInMonth }, (_, i) => {
      const day = i + 1;
      const date = dayjs_instance_of_current_range.value.date(day);
      if (date.isAfter(dayjs(active_schedule_data.value.data[0].end_date)) || date.isBefore(dayjs(active_schedule_data.value.data[0].start_date)))
        return null;
      return {
        data: `${dayjs_instance_of_current_range.value.format('YYYY-MM')}-${String(day).padStart(2, '0')}`,
        header: `${date.format('ddd, DD MMM YYYY')}`,
        width: '150px',
      };
    }).filter(Boolean);
  }
  dynamic_date_items = dynamic_date_items.map(item => ({
    ...item,
    type: 'numeric',
    config: {
      field_type: 'numeric',
      min: 0,
    },
    validator: 'default-validator',
  }));
  return [
    {
      data: 'text',
      header: $t('Activity'),
      readOnly: true,
    },
    ...dynamic_date_items,
  ];
});

const hot_nested_headers = computed(() => {
  return [
    [$t('Activity'), ...hot_columns.value.slice(1).map(column => column.header)],
  ];
});

const is_at_start_limit = computed(() => {
  if (state.interval === 'monthly' || state.interval === 'weekly') {
    return state.form_data.current_range <= dayjs(active_schedule_data.value.data[0].start_date).year();
  }
  else if (state.interval === 'daily') {
    return dayjs_instance_of_current_range.value.isSame(dayjs(active_schedule_data.value.data[0].start_date), 'month');
  }
  return false;
});

const is_at_end_limit = computed(() => {
  if (state.interval === 'monthly' || state.interval === 'weekly') {
    return state.form_data.current_range >= dayjs(active_schedule_data.value.data[0].end_date).year();
  }
  else if (state.interval === 'daily') {
    return dayjs_instance_of_current_range.value.isSame(dayjs(active_schedule_data.value.data[0].end_date), 'month');
  }
  return false;
});

const interval_options = [
  { value: 'monthly', label: $t('Monthly'), on_click: () => handleIntervalChange('monthly') },
  { value: 'weekly', label: $t('Weekly'), on_click: () => handleIntervalChange('weekly') },
  { value: 'daily', label: $t('Daily'), on_click: () => handleIntervalChange('daily') },
];

function updateStateAfterTransformation(new_interval, transformed_data = null) {
  state.interval = new_interval;
  if (transformed_data) {
    state.master_data = transformed_data;
    custom_planned_progress_map = Object.keys(state.master_data).map(activity_id => ({
      id: activity_id,
      custom_planned_progress_data: state.master_data[activity_id],
    }));
    custom_planned_progress_map = keyBy(custom_planned_progress_map, 'id');
    state.hot_data = buildHotDataFromHierarchy(state.activity_hierarchy_map);
  }
  info_modal.close();
}

function aggregateDataByPeriod(period_type) {
  const transformed_data = {};
  Object.keys(state.master_data).forEach((activity_id) => {
    const current_task = $g.value.getTask(activity_id);
    const activity_start = dayjs(current_task.planned_start);
    const activity_finish = dayjs(current_task.planned_finish);
    transformed_data[activity_id] = {};
    Object.keys(state.master_data[activity_id]).forEach((date) => {
      const period_start = dayjs(date, 'YYYY-MM-DD').startOf(period_type);
      if (period_start.isBefore(activity_start, period_type) || period_start.isAfter(activity_finish, period_type)) {
        return;
      }
      const period_key = period_start.format('YYYY-MM-DD');
      if (!transformed_data[activity_id][period_key]) {
        transformed_data[activity_id][period_key] = 0;
      }
      transformed_data[activity_id][period_key] += state.master_data[activity_id][date];
    });
  });
  return transformed_data;
}

function transformMonthlyToWeekly() {
  const transformed_data = {};
  Object.keys(state.master_data).forEach((activity_id) => {
    const current_task = $g.value.getTask(activity_id);
    transformed_data[activity_id] = {};
    Object.keys(state.master_data[activity_id]).forEach((date) => {
      const end_of_month = dayjs(date, 'YYYY-MM-DD').endOf('month');
      let start_of_week = end_of_month.startOf('week');
      if (start_of_week.isAfter(dayjs(current_task.planned_finish))) {
        while (start_of_week.isAfter(dayjs(current_task.planned_finish))) {
          start_of_week = start_of_week.subtract(1, 'week');
        }
      }
      if (start_of_week.month() !== end_of_month.month()) {
        return;
      }
      transformed_data[activity_id][start_of_week.format('YYYY-MM-DD')] = state.master_data[activity_id][date];
    });
  });
  return transformed_data;
}

function transformMonthlyOrWeeklyToDaily(type) {
  const transformed_data = {};
  Object.keys(state.master_data).forEach((activity_id) => {
    const current_task = $g.value.getTask(activity_id);
    const activity_start = dayjs(current_task.planned_start);
    const activity_finish = dayjs(current_task.planned_finish);
    transformed_data[activity_id] = {};
    Object.keys(state.master_data[activity_id]).forEach((date) => {
      const original_date = dayjs(date, 'YYYY-MM-DD');
      let target_date = null;
      if (original_date.isSameOrAfter(activity_start) && original_date.isSameOrBefore(activity_finish)) {
        target_date = original_date.format('YYYY-MM-DD');
      }
      else {
        const range_start = original_date.startOf(type);
        if (activity_start.isSame(range_start, type)) {
          target_date = activity_start.format('YYYY-MM-DD');
        }
      }
      if (target_date && dayjs(target_date).isSameOrAfter(activity_start) && dayjs(target_date).isSameOrBefore(activity_finish)) {
        transformed_data[activity_id][target_date] = state.master_data[activity_id][date];
      }
    });
  });
  return transformed_data;
}

function getCurrentCase(current_interval, new_interval) {
  const case_key = `${current_interval}_to_${new_interval}`;

  const text = `
    <div class="text-gray-700 text-sm">
      ${$t('Changing the interval requires recalculation of existing values. Some precision may be lost. Do you want to continue?')}
    </div>
  `;
  const cases = {
    // Aggregation cases
    daily_to_weekly: {
      text,
      action: () => updateStateAfterTransformation(new_interval, aggregateDataByPeriod('week')),
    },
    daily_to_monthly: {
      text,
      action: () => updateStateAfterTransformation(new_interval, aggregateDataByPeriod('month')),
    },
    weekly_to_monthly: {
      text,
      action: () => updateStateAfterTransformation(new_interval, aggregateDataByPeriod('month')),
    },
    // Disaggregation cases
    monthly_to_daily: {
      text,
      action: () => updateStateAfterTransformation(new_interval, transformMonthlyOrWeeklyToDaily('month')),
    },
    weekly_to_daily: {
      text,
      action: () => updateStateAfterTransformation(new_interval, transformMonthlyOrWeeklyToDaily('week')),
    },
    monthly_to_weekly: {
      text,
      action: () => updateStateAfterTransformation(new_interval, transformMonthlyToWeekly()),
    },
  };

  return cases[case_key];
}

function handleIntervalChange(new_interval) {
  const current_interval = state.interval;
  if (current_interval === new_interval)
    return;
  const current_case = getCurrentCase(current_interval, new_interval);
  info_modal.patchOptions({
    slots: {
      content: current_case.text,
    },
    attrs: {
      header: `${$t('Switching interval')} ${state.is_dirty ? $t('with unsaved changes') : ''}`,
      confirm: () => current_case.action(),
    },
  });
  info_modal.open();
}

function onRangeChange(payload) {
  state.hot_instance = null;
  state.form_data.current_range = payload;
}

function handleRange(direction) {
  if ((direction === -1 && is_at_start_limit.value) || (direction === 1 && is_at_end_limit.value)) {
    return;
  }

  state.hot_instance = null;
  if (state.interval === 'monthly' || state.interval === 'weekly') {
    state.form_data.current_range += direction;
  }
  else if (state.interval === 'daily') {
    state.form_data.current_range = {
      year: state.form_data.current_range.year,
      month: state.form_data.current_range.month + direction,
    };
    if (state.form_data.current_range.month < 0) {
      state.form_data.current_range.year--;
      state.form_data.current_range.month = 11;
    }
    else if (state.form_data.current_range.month > 11) {
      state.form_data.current_range.year++;
      state.form_data.current_range.month = 0;
    }
  }
}

function createActivityHierarchy(parent_id, parent_obj) {
  const children = active_schedule_data.value.data.filter(activity => activity.parent === parent_id);
  children.forEach((child) => {
    parent_obj[child.id] = {};
    createActivityHierarchy(child.id, parent_obj[child.id]);
  });
}

function buildHotDataFromHierarchy(activity_hierarchy_map) {
  return Object.entries(activity_hierarchy_map).map(([id, children]) => {
    const task = $g.value.getTask(id);
    let node = { id, text: task.text };
    state.longest_wbs_code_length = Math.max(state.longest_wbs_code_length, $g.value.getWBSCode(task).length);
    if (custom_planned_progress_map[id]) {
      node = { ...node, ...custom_planned_progress_map[id].custom_planned_progress_data };
    }
    if (children && Object.keys(children).length > 0) {
      node.__children = buildHotDataFromHierarchy(children);
    }
    return node;
  });
}

function cellsConfiguration(row, col) {
  const cell_properties = {};
  const row_data = state.hot_instance?.getSourceDataAtRow(row);

  if (row_data) {
    const task = $g.value.getTask(row_data.id);
    if ([$g.value.config.types.project, $g.value.config.types.milestone].includes(task.type)) {
      cell_properties.readOnly = true;
      cell_properties.className = 'read-only-cell';
    }
    else if (col > 0) {
      const column = hot_columns.value[col];
      if (column && column.data && task.planned_start && task.planned_finish) {
        const column_date = dayjs(column.data, 'YYYY-MM-DD');
        const planned_start = dayjs(task.planned_start);
        const planned_finish = dayjs(task.planned_finish).subtract(1, 'day');
        let unit;
        switch (state.interval) {
          case 'daily':
            unit = 'day';
            break;
          case 'weekly':
            unit = 'week';
            break;
          case 'monthly':
            unit = 'month';
            break;
        }
        if (planned_start.isValid() && planned_finish.isValid() && (column_date.isBefore(planned_start, unit) || column_date.isAfter(planned_finish, unit))) {
          cell_properties.readOnly = true;
          cell_properties.className = 'read-only-cell';
        }
      }
    }
  }

  return cell_properties;
}

function afterChange(changes) {
  if (!changes || !state.hot_instance)
    return;
  state.is_dirty = true;
  changes.forEach((change) => {
    const row_data = state.hot_instance?.getSourceDataAtRow(state.hot_instance?.toPhysicalRow(change[0]));
    if (!state.master_data[row_data.id])
      state.master_data[row_data.id] = {};
    state.master_data[row_data.id][change[1]] = change[3];
  });
}

function beforeKeyDown(event) {
  if (event.ctrlKey || event.metaKey) {
    return;
  }

  const reg = /^[\d.,]+$/;
  if (!reg.test(event.key) && event.key !== 'Backspace')
    event.preventDefault();
}

function rowHeaders(row) {
  const row_data = state.hot_instance?.getSourceDataAtRow(row);
  if (row_data?.id)
    return $g.value.getWBSCode($g.value.getTask(row_data.id));
  return null;
}

function onClose() {
  if (state.is_dirty) {
    info_modal.patchOptions({
      slots: {
        content: `<div class="text-sm text-gray-700">${$t(`You're about to exit without saving your work. Do you want to continue?`)}</div>`,
      },
      attrs: {
        header: $t('You have unsaved changes'),
        confirm: () => emit('close'),
      },
    });
    info_modal.open();
  }
  else {
    emit('close');
  }
}

async function onSave() {
  state.is_saving = true;
  const payload = [];
  Object.keys(state.master_data).forEach((activity_id) => {
    state.master_data[activity_id] = Object.fromEntries(Object.entries(state.master_data[activity_id]).filter(([_key, value]) => value !== null));
    payload.push({
      id: activity_id,
      custom_planned_progress_data: state.master_data[activity_id],
      custom_planned_progress_config: {
        interval: state.interval,
      },
      reset: Object.keys(state.master_data[activity_id]).length === 0,
    });
  });
  if (payload.length)
    await update_custom_planned_progress(payload);
  state.is_dirty = false;
  state.is_saving = false;
}

watch(() => state.interval, (new_interval, old_interval) => {
  state.hot_instance = null;
  state.is_dirty = true;
  if ((old_interval === 'monthly' || old_interval === 'weekly') && new_interval === 'daily') {
    state.form_data.current_range = {
      year: state.form_data.current_range,
      month: state.form_data.current_range === dayjs(active_schedule_data.value.data[0].start_date).year() ? dayjs(active_schedule_data.value.data[0].start_date).month() : 0,
    };
  }
  else if (old_interval === 'daily' && (new_interval === 'monthly' || new_interval === 'weekly')) {
    state.form_data.current_range = state.form_data.current_range.year;
  }
});

onMounted(async () => {
  state.is_loading = true;
  const data = await get_custom_planned_progress();
  const filtered_data = data.filter(custom_planned_progress => custom_planned_progress.planned_progress_completion_type === 'CUSTOM');
  state.interval = filtered_data[0]?.custom_planned_progress_config?.interval || 'monthly';
  await nextTick(); // This nextTick is needed because we want the watcher of the interval to get triggered first
  if (dayjs().isBetween(dayjs(active_schedule_data.value.data[0].start_date), dayjs(active_schedule_data.value.data[0].end_date), 'day', '[]')) {
    if (state.interval === 'daily') {
      state.form_data.current_range = {
        year: dayjs().year(),
        month: dayjs().month(),
      };
    }
    else {
      state.form_data.current_range = dayjs().year();
    }
  }
  else {
    if (state.interval === 'daily') {
      state.form_data.current_range = {
        year: dayjs(active_schedule_data.value.data[0].end_date).year(),
        month: dayjs(active_schedule_data.value.data[0].end_date).month(),
      };
    }
    else {
      state.form_data.current_range = dayjs(active_schedule_data.value.data[0].end_date).year();
    }
  }
  custom_planned_progress_map = keyBy(filtered_data, 'id');
  state.master_data = filtered_data.reduce((acc, curr) => {
    acc[curr.id] = curr.custom_planned_progress_data;
    return acc;
  }, {});
  state.activity_hierarchy_map[props.parentActivityId] = {};
  createActivityHierarchy(props.parentActivityId, state.activity_hierarchy_map[props.parentActivityId]);
  state.hot_data = buildHotDataFromHierarchy(state.activity_hierarchy_map);
  state.is_loading = false;
  state.is_dirty = false;
});
</script>

<template>
  <HawkModalContainer
    content_class="rounded-none w-full h-full"
    :options="{ escToClose: false }"
  >
    <Vueform
      v-model="state.form_data"
      sync
      size="sm"
      :display-errors="false"
      :display-messages="false"
      :columns="{
        default: {
          container: 12,
          label: 3,
          wrapper: 9,
        },
        sm: {
          label: 4,
        },
        md: {
          label: 4,
        },
        lg: {
          label: 4,
        },
      }"
    >
      <div class="col-span-12">
        <HawkModalHeader @close="onClose">
          <template #title>
            <div class="flex flex-col justify-start">
              {{ $t('Set planned values') }}
            </div>
          </template>
          <template #right>
            <div v-show="!state.is_loading" class="h-full flex items-center gap-3 -mr-2">
              <div class="text-xs font-medium text-gray-900 flex items-center gap-3">
                <IconHawkChevronLeft
                  class="cursor-pointer"
                  :class="{
                    '!cursor-not-allowed text-gray-300': is_at_start_limit,
                  }"
                  @click="handleRange(-1)"
                />
                <template v-if="state.interval === 'monthly' || state.interval === 'weekly'">
                  {{ state.form_data.current_range }}
                </template>
                <template v-else-if="state.interval === 'daily'">
                  {{ dayjs_instance_of_current_range.format('MMMM YYYY') }}
                </template>
                <IconHawkChevronRight
                  class="cursor-pointer"
                  :class="{
                    '!cursor-not-allowed text-gray-300': is_at_end_limit,
                  }"
                  @click="handleRange(1)"
                />
              </div>
              <DateTimeElement
                :key="state.interval"
                name="current_range"
                :options="{
                  monthPicker: state.interval === 'daily',
                  yearPicker: state.interval === 'monthly' || state.interval === 'weekly',
                  minDate: dayjs(active_schedule_data.data[0].start_date),
                  maxDate: dayjs(active_schedule_data.data[0].end_date),
                  yearRange: [
                    dayjs(active_schedule_data.data[0].start_date).year(),
                    dayjs(active_schedule_data.data[0].end_date).year(),
                  ],
                }"
                @change="onRangeChange"
              >
                <template #trigger>
                  <IconHawkCalendar class="w-4 h-4 cursor-pointer" />
                </template>
              </DateTimeElement>
              <div class="h-5 w-px bg-gray-300" />
              <HawkMenu position="fixed" :items="interval_options" additional_trigger_classes="!ring-0" class="-mt-1">
                <template #trigger="{ open }">
                  <div class="flex items-center gap-1">
                    <span class="text-xs font-normal text-gray-500">
                      {{ $t('Interval') }}:
                    </span>
                    <span class="text-xs font-medium text-gray-900">
                      {{ interval_options.find((option) => option.value === state.interval)?.label }}
                    </span>
                    <IconHawkChevronUp v-if="open" />
                    <IconHawkChevronDown v-else />
                  </div>
                </template>
              </HawkMenu>
              <div class="h-5 w-px bg-gray-300" />
            </div>
          </template>
        </HawkModalHeader>
        <HawkModalContent class="!h-[calc(100vh-160px)] !max-h-[calc(100vh-160px)] pm-set-planned-values-content">
          <HawkLoader v-if="state.is_loading" container_class="h-full" />
          <HawkHandsOnTable
            v-else
            :key="`${state.interval}-${JSON.stringify(state.form_data.current_range)}`"
            :hot-settings="{
              rowHeaders,
              afterChange,
              beforeKeyDown,
              nestedRows: true,
              bindRowsWithHeaders: true,
              nestedHeaders: hot_nested_headers,
              rowHeaderWidth: state.longest_wbs_code_length * 15,
              cells: cellsConfiguration,
              fixedColumnsStart: 1,
              className: 'htMiddle',
              contextMenu: false,
              dropdownMenu: false,
              columnSorting: false,
              headerClassName: 'htCenter',
            }"
            :right-click-menu="{}"
            :data="state.hot_data"
            :columns="hot_columns"
            :columns-menu="{ items: {} }"
            height="100%"
            @ready="state.hot_instance = $event"
          />
        </HawkModalContent>
        <HawkModalFooter>
          <template #right>
            <div class="flex justify-end w-full col-span-full">
              <ButtonElement
                name="cancel"
                class="mr-4"
                :secondary="true"
                @click="onClose"
              >
                {{ $t('Close') }}
              </ButtonElement>
              <ButtonElement
                name="save"
                :loading="state.is_saving"
                @click="onSave"
              >
                {{ $t('Save') }}
              </ButtonElement>
            </div>
          </template>
        </HawkModalFooter>
      </div>
    </Vueform>
  </HawkModalContainer>
</template>

<style lang="scss">
// .pm-set-planned-values-content {
//   .ht_nestingCollapse::before {
//     mask-image:  url('data:image/svg+xml,%3Csvg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M4 6L8 10L12 6" stroke="%23475467" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/%3E%3C/svg%3E') !important;
//   }

//   .ht_nestingExpand::before {
//     mask-image: url('data:image/svg+xml,%3Csvg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M9 18L15 12L9 6" stroke="%23475467" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/%3E%3C/svg%3E') !important;
//   }

//   .ht_nestingButton {
//     background-color: transparent !important;
//     box-shadow: none !important;
//   }
// }

.pm-set-planned-values-content {
  .ht_nestingCollapse::after {
    content: url('data:image/svg+xml,%3Csvg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M4 6L8 10L12 6" stroke="%23475467" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/%3E%3C/svg%3E') !important;
  }

  .ht_nestingExpand::after {
    content: url('data:image/svg+xml,%3Csvg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M9 18L15 12L9 6" stroke="%23475467" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/%3E%3C/svg%3E') !important;
  }

  .ht_nestingButton {
    margin-top: 1.5px;
  }

  .ht_clone_left .htCore tbody th {
    vertical-align: middle !important;
  }

  th.ht_nestingLevels > div.relative {
    display: flex;
    align-items: center;
    position: relative;

    .ht_nestingLevel_empty {
      width: 20px;
    }

    .ht_nestingButton {
      margin-top: 7px;
      order: 2;
      position: static !important;
      margin-right: 4px;
    }

    .rowHeader {
      order: 3;
    }
  }

  .read-only-cell {
    vertical-align: middle;
    @apply text-gray-600;
  }

  textarea.handsontableInput {
    padding-top: 7.5px;
  }
}
</style>
