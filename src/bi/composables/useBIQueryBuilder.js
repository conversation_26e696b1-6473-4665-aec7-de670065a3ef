import IconHawkArrowDown from '~icons/hawk/arrow-down';
import IconHawk<PERSON>rrowUp from '~icons/hawk/arrow-up';
import calendar from '~icons/hawk/calendar';
import IconHawkFormula from '~icons/hawk/formula';
import IconHawkFunction from '~icons/hawk/function';
import IconHawkHashTwo from '~icons/hawk/hash-two';
import IconHawkTypeOne from '~icons/hawk/type-one';

function getIconsForType(type) {
  const icons_type_map = {
    string: IconHawkTypeOne,
    numeric: IconHawkHashTwo,
    float: IconHawkHashTwo,
    date: calendar,
    function: IconHawkFunction,
    formula: IconHawkFormula,
    ascending: IconHawkArrowUp,
    descending: IconHawkArrowDown,
  };
  return icons_type_map[type] || IconHawkTypeOne;
}

function getOperatorsForType(type) {
  const operators_type_map = {
    text: [{ label: 'Concat', output_type: 'text' }],
    numeric: [{ label: 'Sum', output_type: 'numeric' }, { label: 'Avg', output_type: 'numeric' }, { label: 'Max', output_type: 'numeric' }, { label: 'Min', output_type: 'numeric' }, { label: 'Count', output_type: 'numeric' }],
    date: [{ label: 'Min', output_type: 'date' }, { label: 'Max', output_type: 'date' }],
  };
  return operators_type_map[type] || [];
}

export function useBIQueryBuilder() {
  return {
    getIconsForType,
    getOperatorsForType,
  };
}
