import { acceptHMRUpdate, defineStore } from 'pinia';

export const useBiStore = defineStore('bi', {
  state: () => ({
    chart_builder_config: {},
    chart_builder_data: [
      { Category: 'Design', Quarter: 'Q1', Planned: 10, Actual: 8 },
      { Category: 'Design', Quarter: 'Q1', Planned: 10, Actual: 10 },
      { Category: 'Design', Quarter: 'Q2', Planned: 25, Actual: 22 },
      { Category: 'Engineering', Quarter: 'Q1', Planned: 15, Actual: 14 },
      { Category: 'Engineering', Quarter: 'Q2', Planned: 18, Actual: 16 },
    ],
  }),
  getters: {
  },
  actions: {
  },
});

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useBiStore, import.meta.hot));
