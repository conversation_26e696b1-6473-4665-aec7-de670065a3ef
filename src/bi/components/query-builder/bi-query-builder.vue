<script setup>
import { useCommonImports } from '~/common/composables/common-imports.composable';

const props = defineProps({
  selectedTable: {
    type: Object,
    default: () => ({}),
  },
});

const { $t } = useCommonImports();

const default_selection = {
  fields: [],
  sort_fields: [],
  row_limit: 100,
};

const stages = ref([{
  selected_table: props.selectedTable,
  selection: default_selection,
  tables: [props.selectedTable],
}]);

function getTablesFromStageFields(previous_stage, index) {
  const fieldData = field => ({
    label: field.operator ? `${field.operator.label} ${$t('of')} ${field.column.label}` : field.column.label,
    type: field.operator ? field.operator.output_type : field.column.type,
  });

  const expressionData = field => ({
    label: field.label,
    type: field.output_type,
    expression: field.expression,
    fields: field.fields,
  });

  const columns = previous_stage.selection.fields.map(field => ({
    ...(field.expression ? expressionData(field) : fieldData(field)),
  }));

  const tables = [{
    label: `Stage ${index}`,
    columns,
  }];
  return tables;
}

function addStage() {
  const previous_stage = stages.value[stages.value.length - 1];
  const tables = getTablesFromStageFields(previous_stage, stages.value.length);
  stages.value.push({ tables, selection: default_selection });
}

const stage_header_bg_colors = [
  'bg-purple-600',
  'bg-green-500',
  'bg-orange-500',
  'bg-fuchsia-600',
  'bg-teal-500',
];

function removeSelectionFromStage(stage) {
  const column_table_names = stage.tables.map(table => table.columns.map(column => column.label + table.label)).flat();
  const column_names = stage.tables.map(table => table.columns.map(column => column.label)).flat();
  if (stage.selection.fields?.length)
    stage.selection.fields = stage.selection.fields.filter(field => field.expression ? field.fields.some(f => column_names.includes(f)) : column_table_names.includes(field.column.label + field.table.label));
  if (stage.selection.sort_fields?.length)
    stage.selection.sort_fields = stage.selection.sort_fields.filter(field => column_names.includes(field.column.label + field.table.label));
}

function syncPipelineFrom(index, should_remove_selection = false) {
  for (let i = (index + 1); i < stages.value.length; i++) {
    const tables = getTablesFromStageFields(stages.value[i - 1], i);
    stages.value[i].tables = tables;
    if (should_remove_selection)
      removeSelectionFromStage(stages.value[i]);
  }
}

function onFieldsAdded(index) {
  syncPipelineFrom(index);
}

function onFieldsDeleted(index) {
  syncPipelineFrom(index, true);
  stages.value = stages.value.filter(stage => stage.tables[0].columns.length);
}
</script>

<template>
  <div class="w-full h-full">
    <div v-for="(stage, index) in stages" :key="index" class="mb-2 border-b pb-2 ">
      <div v-if="stages.length > 1" class="flex justify-between items-center">
        <div class="text-sm text-gray-700 font-semibold flex items-center p-2  mb-2 text-white rounded-sm" :class="stage_header_bg_colors[index % stage_header_bg_colors.length]">
          {{ $t('Stage') }} {{ index + 1 }}
        </div>
        <div class="gap-2 flex">
          <bi-query-builder-add-join :primary-dataset="props.selectedTable" />
          <hawk-button icon type="text" size="xs" :disabled="index === 0" @click="stages.splice(index, 1)">
            <IconHawkTrashThree class="text-gray-500 size-4" />
          </hawk-button>
        </div>
      </div>
      <div v-if="selectedTable && index === 0" class="text-sm text-gray-700 font-semibold flex items-center justify-between gap-1 mb-4">
        <div class="flex items-center">
          <IconHawkDatabaseTwo class="size-4 mr-1" /> {{ selectedTable.label }}
        </div>
        <div v-if="stages.length === 1">
          <bi-query-builder-add-join />
        </div>
      </div>
      <bi-query-builder-stage v-model="stage.selection" :selected-table="stage.selected_table" :tables="stage.tables" @fields-deleted="onFieldsDeleted(index)" @fields-added="onFieldsAdded(index)" />
    </div>
    <div class="mt-2 pt-2 pb-4">
      <hawk-button type="light" block :disabled="stages[stages.length - 1]?.selection?.fields?.length === 0" @click="addStage">
        <IconHawkPlus /> {{ $t('Add stage') }}
      </hawk-button>
    </div>
  </div>
</template>
