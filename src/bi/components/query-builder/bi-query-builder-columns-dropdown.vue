<script setup>
import { computed, ref } from 'vue';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';

const props = defineProps({
  tables: {
    type: Array,
    default: () => ([]),
  },
  isDropdown: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['selected', 'expression']);

const { getIconsForType, getOperatorsForType } = useBIQueryBuilder();
const dropdown = ref(null);
const dropdown_content = ref(null);
const sub_menu = ref(null);
const search_input = ref('');
const search_operator_input = ref('');
const hovered_column = ref(null);
const hovered_table = ref(null);
const hovered_column_operators = computed(() => getOperatorsForType(hovered_column.value?.type)?.filter(operator => operator.label.toLowerCase().includes(search_operator_input.value.toLowerCase())));

// Function to detect if the dropdown is at the bottom of the page
function isDropdownAtBottom() {
  if (dropdown.value) {
    const dropdownRect = dropdown.value?.getBoundingClientRect();
    const dropdownContentRect = dropdown_content.value?.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    const dropdownHeight = dropdownContentRect.height;
    return windowHeight - dropdownRect.top < dropdownHeight;
  }
  return false;
}

// Filter the columns based on the search input and return the list of new tables that have columns that match the search input
// It should return only the columns that are matching the search input
function filterColumns(tables, search_input) {
  const new_tables = tables.map((table) => {
    const columns = table.columns.filter((column) => {
      return column.label.toLowerCase().includes(search_input.toLowerCase());
    });
    return { ...table, columns };
  });

  const filtered_tables = new_tables.filter((table) => {
    return table.columns.length > 0;
  });
  // If the search input is empty, return the original tables
  if (!search_input || filtered_tables.length === 0) {
    return tables;
  }
  return filtered_tables;
}

const filtered_tables = computed(() => {
  return filterColumns(props.tables, search_input.value || '');
});

function onColumnHovered(e, column, table) {
  hovered_column.value = column;
  hovered_table.value = table;
  // Set the sub menu position of the top based on the mouse position and the dropdown location in the screen
  const dropdownContentRect = dropdown_content.value?.getBoundingClientRect();
  const rect = e.srcElement.getBoundingClientRect();
  // If the position is at the bottom of the screen, set the top position to the bottom of the dropdown
  sub_menu.value.style.left = `${rect.left + rect.width}px`;
  if ((e.clientY + dropdownContentRect.height) > window.innerHeight) {
    sub_menu.value.style.bottom = `${window.innerHeight - rect.bottom + 5}px`;
    sub_menu.value.style.top = 'auto';
  }
  // If the position is at the top of the screen, set the top position to the top of the dropdown
  else {
    sub_menu.value.style.top = `${rect.top - 10}px`;
    sub_menu.value.style.bottom = 'auto';
  }
}

function onOperatorClicked(operator) {
  emit('selected', { column: hovered_column.value, operator, table: hovered_table.value });
}

function onColumnClicked(column, table) {
  emit('selected', { column, table });
}

function onExpressionClicked() {
  emit('expression');
}
</script>

<template>
  <div ref="dropdown" class="bi-columns-dropdown relative  z-10" :class="{ 'h-10': isDropdown }" @mouseleave="hovered_column = null">
    <div ref="dropdown_content" class="shadow-lg border border-gray-200 rounded-lg  w-full flex flex-col bg-white" :class="{ 'bottom-0': isDropdownAtBottom(), 'top-0': !isDropdownAtBottom(), 'absolute': isDropdown }">
      <div class="relative" :class="{ 'order-3': isDropdownAtBottom() }">
        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
          <IconHawkSearch class="text-gray-400 size-4" />
        </div>
        <input
          v-model="search_input"
          name="input"
          :placeholder="$t('Search for a field')"
          class="block w-full py-1.5 text-gray-900 pl-10 placeholder:text-gray-500 focus:!ring-0 sm:text-sm sm:leading-6 rounded-lg"
        >
      </div>
      <div class="bi-columns-dropdown-content min-h-[300px] max-h-80 overflow-auto scrollbar order-2" :class="{ 'border-t': !isDropdownAtBottom(), 'border-b': isDropdownAtBottom() }">
        <div v-for="table in filtered_tables" :key="table.label">
          <div class="text-sm text-gray-700 font-semibold flex items-center gap-1 px-3 py-2">
            <IconHawkDatabaseTwo class="size-4" /> {{ table.label }}
          </div>
          <div class="pb-2">
            <div v-for="column in table.columns" :key="column.label" class="flex items-center justify-between gap-2 pl-6 py-1 cursor-pointer hover:bg-gray-50 rounded-lg relative" @mouseenter="e => onColumnHovered(e, column, table)" @click="onColumnClicked(column, table)">
              <div class="flex items-center">
                <component :is="getIconsForType(column.type)" class="text-gray-600 size-4 mr-2" />
                <span class="text-sm text-gray-700">{{ column.label }}</span>
              </div>
              <div v-if="getOperatorsForType(column.type).length" class="text-gray-500 size-4 mr-2">
                <IconHawkChevronRight />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div :class="{ 'order-1 border-b': isDropdownAtBottom(), 'border-t order-3': !isDropdownAtBottom() }">
        <span type="text" class="text-sm flex p-2 text-primary-700 hover:bg-gray-50 cursor-pointer rounded-lg" @click="onExpressionClicked">
          <component :is="getIconsForType('formula')" class=" size-4 mr-2" />
          {{ $t('Custom Expression') }}
        </span>
      </div>
    </div>
    <div v-show="hovered_column_operators?.length" ref="sub_menu" class="dropdown-sub-menu shadow-lg border border-gray-200 rounded-lg fixed w-[300px] h-[300px] left-[330px] bg-white z-10">
      <div class="relative" :class="{ 'order-2': isDropdownAtBottom() }">
        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
          <IconHawkSearch class="text-gray-400 size-4" />
        </div>
        <input
          v-model="search_operator_input"
          name="input"
          :placeholder="$t('Search for a function')"
          class="block w-full py-1.5 text-gray-900 pl-10 placeholder:text-gray-500 focus:!ring-0 sm:text-sm sm:leading-6 rounded-lg"
        >
      </div>
      <div class="overflow-auto border-t scrollbar">
        <div v-for="operator in hovered_column_operators" :key="operator.label" class="flex items-center gap-2 pl-6 py-1 cursor-pointer hover:bg-gray-50 rounded-lg relative" @click="onOperatorClicked(operator)">
          <span class="text-sm text-gray-700">{{ operator.label }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
