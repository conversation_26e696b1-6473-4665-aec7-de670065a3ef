<script setup>
const props = defineProps({
  showText: {
    type: String,
    required: true,
  },
  hideText: {
    type: String,
    required: true,
  },
  initialHeight: {
    type: Number,
    default: 300,
  },
  minHeight: {
    type: Number,
    default: 200,
  },
  maxHeight: {
    type: Number,
    default: 600,
  },
});

const emit = defineEmits(['toggle', 'heightChange']);

const state = reactive({
  is_open: false,
  drawer_height: props.initialHeight,
  is_dragging: false,
  drag_start_y: 0,
  drag_start_height: 0,
});

function toggleDrawer() {
  state.is_open = !state.is_open;
  emit('toggle', state.is_open);
}

function startDrag(event) {
  state.is_dragging = true;
  state.drag_start_y = event.clientY;
  state.drag_start_height = state.drawer_height;
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopDrag);
  event.preventDefault();
}

function onDrag(event) {
  if (!state.is_dragging)
    return;

  const delta_y = state.drag_start_y - event.clientY;
  const new_height = Math.max(props.minHeight, Math.min(props.maxHeight, state.drag_start_height + delta_y));
  state.drawer_height = new_height;
  emit('heightChange', new_height);
}

function stopDrag() {
  state.is_dragging = false;
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
}
</script>

<template>
  <div>
    <div
      v-if="!state.is_open"
      class="absolute bottom-0 left-1/2 -translate-x-1/2 w-fit bg-primary-100 border-primary-200 border flex items-center gap-2 py-1 px-2 text-xs font-medium text-gray-700 rounded-t cursor-pointer"
      @click="toggleDrawer"
    >
      <IconHawkArrowUp class="w-4 h-4" />
      {{ props.showText }}
    </div>

    <div
      v-if="state.is_open"
      class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-10 overflow-auto"
      :style="{ height: `${state.drawer_height}px` }"
    >
      <div
        class="w-full h-0.5 bg-gray-100 hover:bg-gray-200 cursor-row-resize flex items-center justify-center border-b border-gray-200"
        @mousedown="startDrag"
      />

      <div
        class="absolute left-1/2 -translate-x-1/2 w-fit bg-primary-100 border-primary-200 border flex items-center gap-2 py-1 px-2 text-xs font-medium text-gray-700 rounded-b cursor-pointer"
        @click="toggleDrawer"
      >
        <IconHawkArrowDown class="w-4 h-4" />
        {{ props.hideText }}
      </div>

      <slot :height="state.drawer_height" />
    </div>
  </div>
</template>
