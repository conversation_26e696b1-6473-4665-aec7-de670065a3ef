<script setup>
import { BI_CHART_COLOR_PALETTES } from '~/bi/helpers/bi-helpers';

const props = defineProps({
  chartType: {
    type: String,
    required: true,
  },
});

const show_legend_position = computed(() => ['bar_chart', 'line_chart', 'area_chart', 'pie_chart', 'doughnut_chart', 'scatter_chart', 'pareto_chart', 'waterfall_chart', 'timeseries_chart'].includes(props.chartType));

const show_values = computed(() => ['bar_chart', 'line_chart', 'area_chart', 'pie_chart', 'doughnut_chart', 'heatmap_chart', 'pyramid_chart', 'funnel_chart', 'pareto_chart', 'waterfall_chart', 'timeseries_chart'].includes(props.chartType));

const show_inner_radius = computed(() => ['doughnut_chart'].includes(props.chartType));

const show_symbol_size = computed(() => ['scatter_chart'].includes(props.chartType));

const show_cell_labels = computed(() => ['heatmap_chart'].includes(props.chartType));

const show_labels = computed(() => ['pyramid_chart', 'funnel_chart'].includes(props.chartType));

const show_conversion_rates = computed(() => ['funnel_chart'].includes(props.chartType));

const show_color_palette = computed(() => ['bar_chart', 'line_chart', 'area_chart', 'pie_chart', 'doughnut_chart', 'scatter_chart', 'pyramid_chart', 'pareto_chart', 'waterfall_chart', 'timeseries_chart'].includes(props.chartType));
</script>

<template>
  <TextElement
    name="title"
    :label="$t('Title')"
    :placeholder="$t('Enter title')"
    description="Main chart title displayed at the top"
  />
  <TextElement
    name="subtitle"
    :label="$t('Subtitle')"
    :placeholder="$t('Enter subtitle')"
    description="Secondary title below the main title"
  />
  <SelectElement
    v-if="show_legend_position"
    name="legend_position"
    label="Legend position"
    :items="{
      none: $t('None'),
      top: $t('Top'),
      bottom: $t('Bottom'),
      left: $t('Left'),
      right: $t('Right'),
    }"
    default="bottom"
    :native="false"
    :can-clear="false"
    :can-deselect="false"
  />
  <SelectElement
    v-if="show_values"
    name="values"
    :label="$t('Values')"
    :items="{
      show: $t('Show'),
      hide: $t('Hide'),
    }"
    default="hide"
    :native="false"
    :can-clear="false"
    :can-deselect="false"
  />
  <TextElement
    input-type="number"
    name="precision"
    label="Precision"
    :conditions="[['values', 'show']]"
    placeholder="Enter precision"
  />
  <SliderElement
    v-if="show_inner_radius"
    name="inner_radius"
    label="Inner radius"
    :conditions="[['chart_type', 'doughnut_chart']]"
    :min="20"
    :max="80"
    default="40"
    :step="1"
  />
  <SliderElement
    v-if="show_symbol_size"
    name="symbol_size"
    label="Symbol size"
    :conditions="[['chart_type', 'scatter_chart']]"
    :min="5"
    :max="50"
    default="10"
    :step="1"
  />
  <ToggleElement
    v-if="show_cell_labels"
    name="cell_labels"
    label="Cell labels"
  />
  <ToggleElement
    v-if="show_labels"
    name="labels"
    label="Labels"
  />
  <ToggleElement
    v-if="show_conversion_rates"
    name="conversion_rates"
    label="Conversion rates"
  />
  <template v-if="show_color_palette">
    <SelectElement
      name="color_palette"
      label="Color palette"
      :items="BI_CHART_COLOR_PALETTES"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
      default="palette1"
    >
      <template #option="{ option }">
        <div class="flex items-center gap-2">
          <div class="flex gap-1 p-1" :class="{ 'rounded bg-gray-900': option.label.dark }">
            <div v-for="color in option.label.colors" :key="color" class="w-3 h-3 rounded-full" :style="{ backgroundColor: color }" />
          </div>
        </div>
      </template>
      <template #single-label="{ value }">
        <div class="w-full flex items-center gap-2 px-2">
          <div class="flex gap-1 p-1" :class="{ 'rounded bg-gray-900': value.label.dark }">
            <div v-for="color in value.label.colors" :key="color" class="w-3 h-3 rounded-full" :style="{ backgroundColor: color }" />
          </div>
        </div>
      </template>
    </SelectElement>
  </template>
</template>
